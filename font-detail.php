<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

$fontId = intval($_GET['id'] ?? 0);
if (!$fontId) {
    header('Location: /');
    exit;
}

$font = $fontManager->getFontById($fontId);
if (!$font) {
    header('Location: /');
    exit;
}

// Get all font weights for this font family
$fontWeights = $fontManager->getFontWeightsByFamily($font['family']);
// 如果没有找到字重，至少包含当前字体
if (empty($fontWeights)) {
    $fontWeights = [$font];
}
$relatedFonts = $fontManager->getRelatedFonts($fontId);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($font['name']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/font-detail.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo"><?php echo APP_NAME; ?></a>
            <div class="header-controls">
                <a href="index.php" class="back-btn">← 返回</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="font-detail-container">
            <!-- Font Preview Section -->
            <section class="font-preview-section">
                <div class="preview-controls">
                    <input type="text" id="previewText" class="preview-text-input"
                           value="<?php echo htmlspecialchars($_SESSION['preview_text']); ?>"
                           placeholder="输入预览文字...">

                    <div class="font-size-control">
                        <label>字体大小:</label>
                        <input type="range" id="fontSizeSlider" min="12" max="120" value="48" step="2">
                        <span id="fontSizeValue">48px</span>
                    </div>
                </div>

                <div class="font-preview-area">
                    <div id="fontPreview" class="font-preview-text"
                         style="font-family: '<?php echo htmlspecialchars($font['family']); ?>'; font-size: 48px;">
                        <?php echo htmlspecialchars($_SESSION['preview_text']); ?>
                    </div>
                </div>
            </section>

            <!-- Font Info Section -->
            <section class="font-info-section">
                <div class="font-header">
                    <h1 class="font-title" id="fontTitle"><?php echo htmlspecialchars($font['name']); ?></h1>
                    <div class="font-actions">
                        <button id="editBtn" class="action-btn">编辑</button>
                        <button id="downloadBtn" class="action-btn primary" data-font-id="<?php echo $font['id']; ?>">下载</button>
                        <button id="favoriteBtn" class="action-btn <?php echo $font['is_favorite'] ? 'active' : ''; ?>"
                                data-font-id="<?php echo $font['id']; ?>">
                            <?php echo $font['is_favorite'] ? '已收藏' : '收藏'; ?>
                        </button>
                        <button id="compareBtn" class="action-btn <?php echo $font['is_in_comparison'] ? 'active' : ''; ?>"
                                data-font-id="<?php echo $font['id']; ?>">
                            <?php echo $font['is_in_comparison'] ? '已对比' : '添加对比'; ?>
                        </button>
                    </div>
                </div>

                <div class="font-meta-grid">
                    <div class="meta-item">
                        <label>字体族:</label>
                        <span><?php echo htmlspecialchars($font['family']); ?></span>
                    </div>
                    <div class="meta-item">
                        <label>样式:</label>
                        <span><?php echo htmlspecialchars($font['style']); ?></span>
                    </div>
                    <div class="meta-item">
                        <label>字重:</label>
                        <div class="weight-selector">
                            <select id="weightSelector" class="weight-select">
                                <?php foreach ($fontWeights as $weightFont): ?>
                                <option value="<?php echo $weightFont['id']; ?>"
                                        data-weight="<?php echo htmlspecialchars($weightFont['weight']); ?>"
                                        data-family="<?php echo htmlspecialchars($weightFont['family']); ?>"
                                        <?php echo $weightFont['id'] == $font['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($weightFont['weight']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="meta-item">
                        <label>分类:</label>
                        <span><?php echo FONT_CATEGORIES[$font['category']] ?? $font['category']; ?></span>
                    </div>
                    <div class="meta-item">
                        <label>下载次数:</label>
                        <span id="downloadCount"><?php echo $font['download_count']; ?></span>
                    </div>
                    <div class="meta-item">
                        <label>上传时间:</label>
                        <span id="uploadDate"><?php echo date('Y-m-d H:i', strtotime($font['upload_date'])); ?></span>
                    </div>
                </div>

                <?php if ($font['description']): ?>
                <div class="font-description">
                    <h3>描述</h3>
                    <p id="fontDescription"><?php echo nl2br(htmlspecialchars($font['description'])); ?></p>
                </div>
                <?php endif; ?>

                <?php if ($font['tags']): ?>
                <div class="font-tags">
                    <h3>标签</h3>
                    <div class="tags-list" id="fontTags">
                        <?php
                        $tags = explode(',', $font['tags']);
                        foreach ($tags as $tag):
                            $tag = trim($tag);
                            if ($tag):
                        ?>
                            <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </div>
                </div>
                <?php endif; ?>
            </section>

            <!-- Font Weights Management Section -->
            <section class="font-weights-section">
                <div class="section-header">
                    <h2>字重管理</h2>
                    <button id="addWeightBtn" class="action-btn primary">添加字重</button>
                </div>

                <div class="weights-grid">
                    <?php foreach ($fontWeights as $weightFont): ?>
                    <div class="weight-item" data-font-id="<?php echo $weightFont['id']; ?>">
                        <div class="weight-preview" style="font-family: '<?php echo htmlspecialchars($weightFont['family']); ?>'; font-weight: <?php echo $weightFont['weight']; ?>;">
                            <?php echo htmlspecialchars($_SESSION['preview_text']); ?>
                        </div>
                        <div class="weight-info">
                            <div class="weight-name"><?php echo htmlspecialchars($weightFont['weight']); ?></div>
                            <div class="weight-meta">
                                <span><?php echo formatFileSize($weightFont['file_size']); ?></span>
                            </div>
                            <div class="weight-actions">
                                <button class="action-btn download-btn" data-font-id="<?php echo $weightFont['id']; ?>">下载</button>
                                <?php if ($weightFont['id'] != $font['id']): ?>
                                <button class="action-btn danger delete-weight-btn" data-font-id="<?php echo $weightFont['id']; ?>">删除</button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Related Fonts Section -->
            <?php if (!empty($relatedFonts)): ?>
            <section class="related-fonts-section">
                <h2>相关字体</h2>
                <div class="related-fonts-grid">
                    <?php foreach ($relatedFonts as $relatedFont): ?>
                    <div class="font-card" onclick="location.href='font-detail.php?id=<?php echo $relatedFont['id']; ?>'">
                        <div class="font-preview" style="font-family: '<?php echo htmlspecialchars($relatedFont['family']); ?>';">
                            <?php echo htmlspecialchars($relatedFont['name']); ?>
                        </div>
                        <div class="font-info">
                            <div class="font-name"><?php echo htmlspecialchars($relatedFont['name']); ?></div>
                            <div class="font-meta">
                                <span><?php echo htmlspecialchars($relatedFont['style']); ?> <?php echo htmlspecialchars($relatedFont['weight']); ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>
        </div>
    </main>

    <!-- Edit Modal -->
    <div id="editModal" class="modal edit-modal">
        <div class="modal-content">
            <h2>编辑字体信息</h2>
            <form id="editForm">
                <input type="hidden" id="fontId" value="<?php echo $font['id']; ?>">

                <div class="form-group">
                    <label for="fontName">字体名称:</label>
                    <input type="text" id="fontName" value="<?php echo htmlspecialchars($font['name']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="fontCategory">分类:</label>
                    <select id="fontCategory">
                        <?php foreach (FONT_CATEGORIES as $key => $name): ?>
                        <option value="<?php echo $key; ?>" <?php echo $font['category'] === $key ? 'selected' : ''; ?>>
                            <?php echo $name; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fontDescription">描述:</label>
                    <textarea id="fontDescription" rows="4"><?php echo htmlspecialchars($font['description'] ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="fontTags">标签 (用逗号分隔):</label>
                    <input type="text" id="fontTagsInput" value="<?php echo htmlspecialchars($font['tags'] ?? ''); ?>"
                           placeholder="例如: 现代, 简洁, 标题">
                </div>

                <div class="form-actions">
                    <button type="button" id="cancelEdit" class="action-btn">取消</button>
                    <button type="submit" class="action-btn primary">保存</button>
                    <button type="button" id="deleteFont" class="action-btn danger">删除字体</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Weight Modal -->
    <div id="addWeightModal" class="modal upload-modal">
        <div class="modal-content">
            <h2>添加字重</h2>
            <p>为 <strong><?php echo htmlspecialchars($font['family']); ?></strong> 字体族添加新的字重文件</p>

            <div id="weightUploadArea" class="upload-area">
                <div class="upload-icon">📁</div>
                <p>点击选择字体文件或拖拽到此处</p>
                <p class="upload-hint">支持 TTF, OTF, WOFF, WOFF2 格式</p>
                <input type="file" id="weightFileInput" class="file-input" accept=".ttf,.otf,.woff,.woff2" multiple>
            </div>

            <div id="weightUploadProgress" class="upload-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p>正在上传...</p>
            </div>

            <div class="form-actions">
                <button type="button" id="cancelAddWeight" class="action-btn">取消</button>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/font-detail.js"></script>
    <!-- 临时调试脚本 -->
    <script>
        console.log('页面加载完成，开始调试...');

        // 检查关键元素
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 加载完成');

            const addWeightBtn = document.getElementById('addWeightBtn');
            console.log('addWeightBtn:', addWeightBtn);

            const modal = document.getElementById('addWeightModal');
            console.log('addWeightModal:', modal);

            if (addWeightBtn) {
                addWeightBtn.addEventListener('click', function() {
                    console.log('添加字重按钮被点击！');
                    if (modal) {
                        modal.style.display = 'flex';
                        modal.classList.add('show');
                        console.log('模态框应该显示了');
                    }
                });
            }
        });
    </script>
</body>
</html>
