<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/font-parser.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只支持POST请求', 405);
}

// 检查是否有文件上传
if (!isset($_FILES['fonts']) || empty($_FILES['fonts']['name'][0])) {
    errorResponse('请选择要上传的字体文件');
}

// 获取字体族名称
$fontFamily = $_POST['family'] ?? '';
if (empty($fontFamily)) {
    errorResponse('字体族名称不能为空');
}

// 确保上传目录存在
if (!is_dir(FONTS_DIR)) {
    mkdir(FONTS_DIR, 0755, true);
}

$uploadedFiles = [];
$errors = [];
$totalUploaded = 0;

// 处理多个文件
$fileCount = count($_FILES['fonts']['name']);

for ($i = 0; $i < $fileCount; $i++) {
    $fileName = $_FILES['fonts']['name'][$i];
    $fileTmpName = $_FILES['fonts']['tmp_name'][$i];
    $fileSize = $_FILES['fonts']['size'][$i];
    $fileError = $_FILES['fonts']['error'][$i];
    
    // 跳过空文件
    if (empty($fileName)) continue;
    
    // 检查上传错误
    if ($fileError !== UPLOAD_ERR_OK) {
        $errors[] = "文件 {$fileName} 上传失败: " . getUploadErrorMessage($fileError);
        continue;
    }
    
    // 检查文件大小
    if ($fileSize > MAX_FILE_SIZE) {
        $errors[] = "文件 {$fileName} 超过最大允许大小 " . formatFileSize(MAX_FILE_SIZE);
        continue;
    }
    
    // 检查文件扩展名
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
        $errors[] = "文件 {$fileName} 格式不支持，只支持: " . implode(', ', ALLOWED_EXTENSIONS);
        continue;
    }
    
    try {
        // 生成唯一文件名
        $uniqueFileName = generateUniqueFilename($fileName, FONTS_DIR);
        $targetPath = FONTS_DIR . $uniqueFileName;
        
        // 移动文件到目标目录
        if (!move_uploaded_file($fileTmpName, $targetPath)) {
            $errors[] = "文件 {$fileName} 保存失败";
            continue;
        }
        
        // 解析字体信息
        $parser = new FontParser($targetPath);
        $fontInfo = $parser->parse();
        
        // 检查字体族是否匹配
        if ($fontInfo['family'] !== $fontFamily) {
            // 如果字体族不匹配，删除已上传的文件
            unlink($targetPath);
            $errors[] = "文件 {$fileName} 的字体族 ({$fontInfo['family']}) 与目标字体族 ({$fontFamily}) 不匹配";
            continue;
        }
        
        // 检查是否已存在相同字重
        $existingFont = $fontManager->db->selectOne(
            'fonts',
            'id',
            'family = :family AND weight = :weight',
            ['family' => $fontFamily, 'weight' => $fontInfo['weight']]
        );
        
        if ($existingFont) {
            unlink($targetPath);
            $errors[] = "字体族 {$fontFamily} 已存在字重 {$fontInfo['weight']}";
            continue;
        }
        
        // 准备数据库数据
        $fontData = [
            'name' => $fontInfo['name'],
            'filename' => $uniqueFileName,
            'filepath' => $targetPath,
            'family' => $fontInfo['family'],
            'style' => $fontInfo['style'],
            'weight' => $fontInfo['weight'],
            'category' => $fontInfo['category'],
            'file_size' => $fontInfo['file_size']
        ];
        
        // 保存到数据库
        $fontId = $fontManager->addFont($fontData);
        
        if ($fontId) {
            $uploadedFiles[] = [
                'id' => $fontId,
                'name' => $fontInfo['name'],
                'weight' => $fontInfo['weight'],
                'file_size' => $fontInfo['file_size']
            ];
            $totalUploaded++;
        } else {
            unlink($targetPath);
            $errors[] = "文件 {$fileName} 数据库保存失败";
        }
        
    } catch (Exception $e) {
        if (file_exists($targetPath)) {
            unlink($targetPath);
        }
        $errors[] = "文件 {$fileName} 处理失败: " . $e->getMessage();
    }
}

// 返回结果
if ($totalUploaded > 0) {
    $response = [
        'success' => true,
        'message' => "成功上传 {$totalUploaded} 个字重文件",
        'total_uploaded' => $totalUploaded,
        'uploaded_files' => $uploadedFiles
    ];
    
    if (!empty($errors)) {
        $response['warnings'] = $errors;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
} else {
    errorResponse('没有文件上传成功' . (!empty($errors) ? ': ' . implode('; ', $errors) : ''));
}

function getUploadErrorMessage($errorCode) {
    switch ($errorCode) {
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return '文件太大';
        case UPLOAD_ERR_PARTIAL:
            return '文件只有部分被上传';
        case UPLOAD_ERR_NO_FILE:
            return '没有文件被上传';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '找不到临时文件夹';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return '文件上传被扩展程序阻止';
        default:
            return '未知错误';
    }
}

function generateUniqueFilename($originalName, $directory) {
    $pathInfo = pathinfo($originalName);
    $baseName = $pathInfo['filename'];
    $extension = $pathInfo['extension'];
    
    $counter = 1;
    $fileName = $originalName;
    
    while (file_exists($directory . $fileName)) {
        $fileName = $baseName . '_' . $counter . '.' . $extension;
        $counter++;
    }
    
    return $fileName;
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $size = $bytes;
    $unitIndex = 0;
    
    while ($size >= 1024 && $unitIndex < count($units) - 1) {
        $size /= 1024;
        $unitIndex++;
    }
    
    return round($size, 2) . ' ' . $units[$unitIndex];
}

function successResponse($data = [], $message = '操作成功') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function errorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>
