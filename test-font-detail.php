<?php
// 简单的测试页面来验证字体详情功能
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

echo "<h1>字体详情功能测试</h1>";

// 测试数据库连接
try {
    $db = new Database();
    echo "<p>✅ 数据库连接成功</p>";
} catch (Exception $e) {
    echo "<p>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    exit;
}

// 测试字体管理器
try {
    $fontManager = new FontManager();
    echo "<p>✅ 字体管理器初始化成功</p>";
} catch (Exception $e) {
    echo "<p>❌ 字体管理器初始化失败: " . $e->getMessage() . "</p>";
    exit;
}

// 获取第一个字体进行测试
$fonts = $fontManager->getFonts('', '', 1, 1);
if (empty($fonts['fonts'])) {
    echo "<p>❌ 没有找到字体数据，请先上传一些字体文件</p>";
    exit;
}

$testFont = $fonts['fonts'][0];
echo "<p>✅ 找到测试字体: " . htmlspecialchars($testFont['name']) . "</p>";

// 测试 getFontWeightsByFamily 方法
try {
    $fontWeights = $fontManager->getFontWeightsByFamily($testFont['family']);
    echo "<p>✅ getFontWeightsByFamily 方法工作正常，找到 " . count($fontWeights) . " 个字重</p>";
    
    foreach ($fontWeights as $weight) {
        echo "<p>  - 字重: " . htmlspecialchars($weight['weight']) . " (ID: " . $weight['id'] . ")</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ getFontWeightsByFamily 方法失败: " . $e->getMessage() . "</p>";
}

// 测试 formatFileSize 函数
try {
    $testSize = formatFileSize(1024 * 1024); // 1MB
    echo "<p>✅ formatFileSize 函数工作正常: " . $testSize . "</p>";
} catch (Exception $e) {
    echo "<p>❌ formatFileSize 函数失败: " . $e->getMessage() . "</p>";
}

// 测试会话
if (isset($_SESSION['preview_text'])) {
    echo "<p>✅ 会话工作正常，预览文字: " . htmlspecialchars($_SESSION['preview_text']) . "</p>";
} else {
    echo "<p>❌ 会话问题，预览文字未设置</p>";
}

echo "<h2>JavaScript 测试</h2>";
echo "<button id='testBtn' onclick='testJS()'>测试 JavaScript</button>";
echo "<div id='testResult'></div>";

echo "<script>
function testJS() {
    const result = document.getElementById('testResult');
    
    // 测试元素查找
    const addWeightBtn = document.getElementById('addWeightBtn');
    if (addWeightBtn) {
        result.innerHTML += '<p>✅ 找到 addWeightBtn 元素</p>';
    } else {
        result.innerHTML += '<p>❌ 未找到 addWeightBtn 元素</p>';
    }
    
    // 测试模态框元素
    const modal = document.getElementById('addWeightModal');
    if (modal) {
        result.innerHTML += '<p>✅ 找到 addWeightModal 元素</p>';
    } else {
        result.innerHTML += '<p>❌ 未找到 addWeightModal 元素</p>';
    }
    
    result.innerHTML += '<p>JavaScript 测试完成</p>';
}
</script>";

echo "<h2>模拟字体详情页面元素</h2>";
echo "<button id='addWeightBtn' class='action-btn primary'>添加字重</button>";
echo "<div id='addWeightModal' class='modal' style='display: none;'>模态框内容</div>";

echo "<p><a href='font-detail.php?id=" . $testFont['id'] . "'>访问实际字体详情页面</a></p>";
?>
