<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

class FontManager {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function getFonts($category = '', $search = '', $page = 1, $limit = FONTS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        $where = [];
        $params = [];
        
        if ($category && $category !== 'all') {
            $where[] = 'category = :category';
            $params['category'] = $category;
        }
        
        if ($search) {
            $where[] = '(name LIKE :search OR family LIKE :search OR tags LIKE :search)';
            $params['search'] = '%' . $search . '%';
        }
        
        $whereClause = $where ? implode(' AND ', $where) : '';
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM fonts" . ($whereClause ? " WHERE $whereClause" : "");
        $countStmt = $this->db->query($countSql, $params);
        $total = $countStmt->fetch()['total'];
        
        // 获取字体列表
        $fonts = $this->db->select(
            'fonts', 
            '*', 
            $whereClause, 
            $params, 
            'upload_date DESC', 
            "$limit OFFSET $offset"
        );
        
        // 检查每个字体是否被收藏
        foreach ($fonts as &$font) {
            $font['is_favorite'] = $this->isFavorite($font['id']);
            $font['is_in_comparison'] = $this->isInComparison($font['id']);
        }
        
        return [
            'fonts' => $fonts,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit),
            'limit' => $limit
        ];
    }
    
    public function getFontById($id) {
        $font = $this->db->selectOne('fonts', '*', 'id = :id', ['id' => $id]);
        if ($font) {
            $font['is_favorite'] = $this->isFavorite($id);
            $font['is_in_comparison'] = $this->isInComparison($id);
        }
        return $font;
    }
    
    public function addFont($fontData) {
        return $this->db->insert('fonts', $fontData);
    }
    
    public function updateFont($id, $data) {
        return $this->db->update('fonts', $data, 'id = :id', ['id' => $id]);
    }
    
    public function deleteFont($id) {
        $font = $this->getFontById($id);
        if ($font) {
            // 删除字体文件
            if (file_exists($font['filepath'])) {
                unlink($font['filepath']);
            }
            
            // 删除数据库记录
            return $this->db->delete('fonts', 'id = :id', ['id' => $id]);
        }
        return false;
    }
    
    public function incrementDownloadCount($id) {
        $this->db->query(
            'UPDATE fonts SET download_count = download_count + 1 WHERE id = :id',
            ['id' => $id]
        );
    }
    
    public function getFavorites() {
        $sql = "SELECT f.* FROM fonts f 
                INNER JOIN favorites fav ON f.id = fav.font_id 
                ORDER BY fav.created_at DESC";
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll();
    }
    
    public function addToFavorites($fontId) {
        if (!$this->isFavorite($fontId)) {
            return $this->db->insert('favorites', ['font_id' => $fontId]);
        }
        return false;
    }
    
    public function removeFromFavorites($fontId) {
        return $this->db->delete('favorites', 'font_id = :font_id', ['font_id' => $fontId]);
    }
    
    public function isFavorite($fontId) {
        $result = $this->db->selectOne('favorites', 'id', 'font_id = :font_id', ['font_id' => $fontId]);
        return $result !== null;
    }
    
    public function getComparisons() {
        $sql = "SELECT f.* FROM fonts f 
                INNER JOIN comparisons c ON f.id = c.font_id 
                ORDER BY c.created_at DESC";
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll();
    }
    
    public function addToComparison($fontId) {
        if (!$this->isInComparison($fontId)) {
            return $this->db->insert('comparisons', ['font_id' => $fontId]);
        }
        return false;
    }
    
    public function removeFromComparison($fontId) {
        return $this->db->delete('comparisons', 'font_id = :font_id', ['font_id' => $fontId]);
    }
    
    public function clearComparisons() {
        return $this->db->delete('comparisons', '1=1');
    }
    
    public function isInComparison($fontId) {
        $result = $this->db->selectOne('comparisons', 'id', 'font_id = :font_id', ['font_id' => $fontId]);
        return $result !== null;
    }
    
    public function getCategoryStats() {
        $sql = "SELECT category, COUNT(*) as count FROM fonts GROUP BY category";
        $stmt = $this->db->query($sql);
        $stats = [];
        
        while ($row = $stmt->fetch()) {
            $stats[$row['category']] = $row['count'];
        }
        
        return $stats;
    }
    
    public function getRelatedFonts($fontId, $limit = 6) {
        $font = $this->getFontById($fontId);
        if (!$font) return [];

        // 获取同类别的其他字体
        $fonts = $this->db->select(
            'fonts',
            '*',
            'category = :category AND id != :id',
            ['category' => $font['category'], 'id' => $fontId],
            'RANDOM()',
            $limit
        );

        return $fonts;
    }

    public function getFontWeightsByFamily($family) {
        $fonts = $this->db->select(
            'fonts',
            '*',
            'family = :family',
            ['family' => $family],
            'CAST(weight AS INTEGER)'
        );

        foreach ($fonts as &$font) {
            $font['is_favorite'] = $this->isFavorite($font['id']);
            $font['is_in_comparison'] = $this->isInComparison($font['id']);
        }

        return $fonts;
    }

    public function searchFonts($query, $page = 1, $limit = FONTS_PER_PAGE) {
        return $this->getFonts('', $query, $page, $limit);
    }
}

// 全局字体管理器实例
$fontManager = new FontManager();
