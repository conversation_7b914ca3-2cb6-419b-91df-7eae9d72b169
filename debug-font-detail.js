// 调试版本的字体详情管理器
console.log('开始加载字体详情管理器...');

class DebugFontDetailManager {
    constructor() {
        console.log('FontDetailManager 构造函数开始');
        
        // 检查必要元素
        this.checkElements();
        
        // 初始化
        this.init();
        
        console.log('FontDetailManager 构造函数完成');
    }
    
    checkElements() {
        console.log('检查页面元素...');
        
        const elements = [
            'fontId',
            'addWeightBtn', 
            'cancelAddWeight',
            'addWeightModal',
            'weightUploadArea',
            'weightFileInput',
            'weightSelector'
        ];
        
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                console.log(`✅ 找到元素: ${id}`);
            } else {
                console.log(`❌ 未找到元素: ${id}`);
            }
        });
    }
    
    init() {
        console.log('开始绑定事件...');
        this.bindEvents();
        console.log('事件绑定完成');
    }
    
    bindEvents() {
        // 添加字重按钮
        const addWeightBtn = document.getElementById('addWeightBtn');
        if (addWeightBtn) {
            console.log('绑定添加字重按钮事件');
            addWeightBtn.addEventListener('click', () => {
                console.log('添加字重按钮被点击');
                this.showAddWeightModal();
            });
        } else {
            console.log('❌ 添加字重按钮不存在');
        }

        // 取消添加字重
        const cancelAddWeight = document.getElementById('cancelAddWeight');
        if (cancelAddWeight) {
            console.log('绑定取消按钮事件');
            cancelAddWeight.addEventListener('click', () => {
                console.log('取消按钮被点击');
                this.closeAddWeightModal();
            });
        } else {
            console.log('❌ 取消按钮不存在');
        }
        
        // 字重选择器
        const weightSelector = document.getElementById('weightSelector');
        if (weightSelector) {
            console.log('绑定字重选择器事件');
            weightSelector.addEventListener('change', (e) => {
                console.log('字重选择器改变:', e.target.value);
            });
        } else {
            console.log('❌ 字重选择器不存在');
        }
    }
    
    showAddWeightModal() {
        console.log('显示添加字重模态框');
        const modal = document.getElementById('addWeightModal');
        if (modal) {
            modal.classList.add('show');
            console.log('✅ 模态框显示成功');
        } else {
            console.log('❌ 模态框元素不存在');
        }
    }
    
    closeAddWeightModal() {
        console.log('关闭添加字重模态框');
        const modal = document.getElementById('addWeightModal');
        if (modal) {
            modal.classList.remove('show');
            console.log('✅ 模态框关闭成功');
        } else {
            console.log('❌ 模态框元素不存在');
        }
    }
}

// 等待 DOM 加载完成
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM 加载完成，初始化字体详情管理器');
    
    // 检查是否在字体详情页面
    if (document.getElementById('fontId')) {
        console.log('检测到字体详情页面，初始化管理器');
        window.debugFontDetailManager = new DebugFontDetailManager();
    } else {
        console.log('不是字体详情页面，跳过初始化');
    }
});

// 全局调试函数
window.debugFontDetail = {
    checkElements: function() {
        const elements = [
            'fontId', 'addWeightBtn', 'cancelAddWeight', 
            'addWeightModal', 'weightUploadArea', 'weightFileInput'
        ];
        
        elements.forEach(id => {
            const element = document.getElementById(id);
            console.log(`${id}: ${element ? '✅ 存在' : '❌ 不存在'}`);
        });
    },
    
    testModal: function() {
        const modal = document.getElementById('addWeightModal');
        if (modal) {
            modal.classList.toggle('show');
            console.log('模态框状态切换');
        } else {
            console.log('模态框不存在');
        }
    },
    
    testButton: function() {
        const btn = document.getElementById('addWeightBtn');
        if (btn) {
            btn.click();
            console.log('模拟按钮点击');
        } else {
            console.log('按钮不存在');
        }
    }
};

console.log('调试脚本加载完成');
console.log('可用调试函数: debugFontDetail.checkElements(), debugFontDetail.testModal(), debugFontDetail.testButton()');
