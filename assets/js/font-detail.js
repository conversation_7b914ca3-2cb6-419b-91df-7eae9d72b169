class FontDetailManager {
    constructor() {
        this.fontId = document.getElementById('fontId').value;
        this.currentFontFamily = '';
        this.basePath = this.getBasePath();
        this.init();
    }

    getBasePath() {
        // Get the current page's directory path
        const currentPath = window.location.pathname;
        const pathParts = currentPath.split('/');

        // Remove the filename (like font-detail.php) from the path
        if (pathParts[pathParts.length - 1].includes('.')) {
            pathParts.pop();
        }

        // Join back to get the base directory
        const basePath = pathParts.join('/');
        return basePath === '/' ? '' : basePath;
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 预览文字更新
        document.getElementById('previewText').addEventListener('input', (e) => {
            this.updatePreview(e.target.value);
        });

        // 字体大小调整
        document.getElementById('fontSizeSlider').addEventListener('input', (e) => {
            this.updateFontSize(e.target.value);
        });

        // 下载按钮
        document.getElementById('downloadBtn').addEventListener('click', () => {
            this.downloadFont();
        });

        // 收藏按钮
        document.getElementById('favoriteBtn').addEventListener('click', () => {
            this.toggleFavorite();
        });

        // 对比按钮
        document.getElementById('compareBtn').addEventListener('click', () => {
            this.toggleComparison();
        });

        // 编辑按钮
        document.getElementById('editBtn').addEventListener('click', () => {
            this.showEditModal();
        });

        // 编辑表单
        document.getElementById('editForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveFont();
        });

        // 取消编辑
        document.getElementById('cancelEdit').addEventListener('click', () => {
            this.closeEditModal();
        });

        // 删除字体
        document.getElementById('deleteFont').addEventListener('click', () => {
            this.deleteFont();
        });

        // 字重选择器
        const weightSelector = document.getElementById('weightSelector');
        if (weightSelector) {
            weightSelector.addEventListener('change', (e) => {
                this.switchWeight(e.target.value);
            });

            // 获取当前字体族
            const selectedOption = weightSelector.options[weightSelector.selectedIndex];
            this.currentFontFamily = selectedOption.dataset.family;
        }

        // 添加字重按钮
        const addWeightBtn = document.getElementById('addWeightBtn');
        if (addWeightBtn) {
            addWeightBtn.addEventListener('click', () => {
                this.showAddWeightModal();
            });
        }

        // 取消添加字重
        const cancelAddWeight = document.getElementById('cancelAddWeight');
        if (cancelAddWeight) {
            cancelAddWeight.addEventListener('click', () => {
                this.closeAddWeightModal();
            });
        }

        // 字重文件上传
        this.setupWeightUpload();

        // 删除字重按钮
        document.querySelectorAll('.delete-weight-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteWeight(btn.dataset.fontId);
            });
        });

        // 字重下载按钮
        document.querySelectorAll('.weight-item .download-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.downloadFont(btn.dataset.fontId);
            });
        });
    }

    updatePreview(text) {
        const preview = document.getElementById('fontPreview');
        preview.textContent = text || preview.dataset.fontName || 'Preview Text';
    }

    updateFontSize(size) {
        document.getElementById('fontSizeValue').textContent = size + 'px';
        document.getElementById('fontPreview').style.fontSize = size + 'px';
    }

    async downloadFont(fontId = null) {
        const targetFontId = fontId || this.fontId;

        try {
            const response = await fetch(`${this.basePath}/api/fonts.php?action=download&id=${targetFontId}`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;

                // 从响应头获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                const filename = contentDisposition
                    ? contentDisposition.split('filename=')[1].replace(/"/g, '')
                    : 'font-file';

                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败，请重试');
        }
    }

    async toggleFavorite() {
        const button = document.getElementById('favoriteBtn');
        const isFavorite = button.classList.contains('active');
        const action = isFavorite ? 'remove' : 'add';
        const method = isFavorite ? 'DELETE' : 'POST';

        try {
            const response = await fetch(`${this.basePath}/api/favorites.php?action=${action}`, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ font_id: this.fontId })
            });

            const data = await response.json();

            if (data.success) {
                button.classList.toggle('active');
                button.textContent = button.classList.contains('active') ? '已收藏' : '收藏';
            } else {
                alert(data.message || '操作失败');
            }
        } catch (error) {
            console.error('收藏操作失败:', error);
            alert('操作失败，请重试');
        }
    }

    async toggleComparison() {
        const button = document.getElementById('compareBtn');
        const isInComparison = button.classList.contains('active');
        const action = isInComparison ? 'remove' : 'add';
        const method = isInComparison ? 'DELETE' : 'POST';

        try {
            const response = await fetch(`${this.basePath}/api/compare.php?action=${action}`, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ font_id: this.fontId })
            });

            const data = await response.json();

            if (data.success) {
                button.classList.toggle('active');
                button.textContent = button.classList.contains('active') ? '已对比' : '添加对比';
            } else {
                alert(data.message || '操作失败');
            }
        } catch (error) {
            console.error('对比操作失败:', error);
            alert('操作失败，请重试');
        }
    }

    showEditModal() {
        document.getElementById('editModal').classList.add('show');
    }

    closeEditModal() {
        document.getElementById('editModal').classList.remove('show');
    }

    async saveFont() {
        const formData = {
            id: this.fontId,
            name: document.getElementById('fontName').value.trim(),
            category: document.getElementById('fontCategory').value,
            description: document.getElementById('fontDescription').value.trim(),
            tags: document.getElementById('fontTagsInput').value.trim()
        };

        if (!formData.name) {
            alert('字体名称不能为空');
            return;
        }

        try {
            const response = await fetch(`${this.basePath}/api/fonts.php?action=update`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                // 更新页面显示
                document.getElementById('fontTitle').textContent = formData.name;

                // 更新描述
                const descriptionElement = document.getElementById('fontDescription');
                if (descriptionElement) {
                    descriptionElement.innerHTML = formData.description.replace(/\n/g, '<br>');
                }

                // 更新标签
                const tagsElement = document.getElementById('fontTags');
                if (tagsElement && formData.tags) {
                    const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                    tagsElement.innerHTML = tags.map(tag => `<span class="tag">${tag}</span>`).join('');
                }

                this.closeEditModal();
                alert('字体信息已更新');
            } else {
                alert(data.message || '更新失败');
            }
        } catch (error) {
            console.error('更新失败:', error);
            alert('更新失败，请重试');
        }
    }

    async deleteFont() {
        if (!confirm('确定要删除这个字体吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`${this.basePath}/api/fonts.php?action=delete`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: this.fontId })
            });

            const data = await response.json();

            if (data.success) {
                alert('字体已删除');
                window.location.href = 'index.php';
            } else {
                alert(data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败，请重试');
        }
    }

    // 字重切换
    async switchWeight(fontId) {
        try {
            const response = await fetch(`${this.basePath}/api/fonts.php?action=detail&id=${fontId}`);
            const data = await response.json();

            if (data.success) {
                const font = data.data.font;

                // 更新预览
                const preview = document.getElementById('fontPreview');
                preview.style.fontFamily = `'${font.family}'`;
                preview.style.fontWeight = font.weight;

                // 更新页面信息
                document.getElementById('fontTitle').textContent = font.name;
                document.getElementById('downloadCount').textContent = font.download_count;
                document.getElementById('uploadDate').textContent = new Date(font.upload_date).toLocaleString('zh-CN');

                // 更新当前字体ID
                this.fontId = fontId;
                document.getElementById('fontId').value = fontId;

                // 更新按钮状态
                const favoriteBtn = document.getElementById('favoriteBtn');
                favoriteBtn.classList.toggle('active', font.is_favorite);
                favoriteBtn.textContent = font.is_favorite ? '已收藏' : '收藏';

                const compareBtn = document.getElementById('compareBtn');
                compareBtn.classList.toggle('active', font.is_in_comparison);
                compareBtn.textContent = font.is_in_comparison ? '已对比' : '添加对比';

                // 更新URL
                window.history.replaceState({}, '', `font-detail.php?id=${fontId}`);
            }
        } catch (error) {
            console.error('切换字重失败:', error);
            alert('切换字重失败，请重试');
        }
    }

    // 显示添加字重模态框
    showAddWeightModal() {
        const modal = document.getElementById('addWeightModal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    // 关闭添加字重模态框
    closeAddWeightModal() {
        const modal = document.getElementById('addWeightModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    // 设置字重上传
    setupWeightUpload() {
        const uploadArea = document.getElementById('weightUploadArea');
        const fileInput = document.getElementById('weightFileInput');

        if (!uploadArea || !fileInput) {
            return; // 如果元素不存在，直接返回
        }

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleWeightFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            this.handleWeightFiles(e.target.files);
        });
    }

    // 处理字重文件上传
    async handleWeightFiles(files) {
        if (files.length === 0) return;

        const formData = new FormData();
        formData.append('family', this.currentFontFamily);

        for (let file of files) {
            formData.append('fonts[]', file);
        }

        // 显示上传进度
        this.showWeightUploadProgress();

        try {
            const xhr = new XMLHttpRequest();

            // 监听上传进度
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    this.updateWeightUploadProgress(percentComplete, '正在上传...');
                }
            });

            // 监听响应
            xhr.addEventListener('load', () => {
                this.hideWeightUploadProgress();

                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            alert(`成功上传 ${data.total_uploaded} 个字重文件`);
                            this.closeAddWeightModal();
                            // 刷新页面以显示新的字重
                            window.location.reload();
                        } else {
                            alert(data.message || '上传失败');
                        }
                    } catch (e) {
                        alert('上传失败: 服务器返回无效响应');
                    }
                } else {
                    alert(`上传失败: HTTP ${xhr.status}`);
                }
            });

            xhr.addEventListener('error', () => {
                this.hideWeightUploadProgress();
                alert('上传失败: 网络错误');
            });

            xhr.open('POST', `${this.basePath}/api/upload-weight.php`);
            xhr.send(formData);

        } catch (error) {
            this.hideWeightUploadProgress();
            console.error('上传失败:', error);
            alert(`上传失败: ${error.message}`);
        }
    }

    // 显示字重上传进度
    showWeightUploadProgress() {
        const uploadArea = document.getElementById('weightUploadArea');
        const uploadProgress = document.getElementById('weightUploadProgress');

        uploadArea.style.display = 'none';
        uploadProgress.style.display = 'block';
        this.updateWeightUploadProgress(0, '准备上传...');
    }

    // 更新字重上传进度
    updateWeightUploadProgress(percent, message) {
        const progressFill = document.querySelector('#weightUploadProgress .progress-fill');
        const progressText = document.querySelector('#weightUploadProgress p');

        if (progressFill) {
            progressFill.style.width = percent + '%';
        }
        if (progressText) {
            progressText.textContent = message + ` (${Math.round(percent)}%)`;
        }
    }

    // 隐藏字重上传进度
    hideWeightUploadProgress() {
        const uploadArea = document.getElementById('weightUploadArea');
        const uploadProgress = document.getElementById('weightUploadProgress');

        uploadArea.style.display = 'block';
        uploadProgress.style.display = 'none';
    }

    // 删除字重
    async deleteWeight(fontId) {
        if (!confirm('确定要删除这个字重吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`${this.basePath}/api/fonts.php?action=delete`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: fontId })
            });

            const data = await response.json();

            if (data.success) {
                alert('字重已删除');
                // 移除对应的字重项
                const weightItem = document.querySelector(`[data-font-id="${fontId}"]`);
                if (weightItem) {
                    weightItem.remove();
                }

                // 更新字重选择器
                const weightSelector = document.getElementById('weightSelector');
                const optionToRemove = weightSelector.querySelector(`option[value="${fontId}"]`);
                if (optionToRemove) {
                    optionToRemove.remove();
                }
            } else {
                alert(data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败，请重试');
        }
    }
}

// 初始化字体详情管理器
document.addEventListener('DOMContentLoaded', () => {
    new FontDetailManager();
});
