/* Font Detail Page Specific Styles */

/* Weight Selector */
.weight-selector {
    position: relative;
}

.weight-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s;
}

.weight-select:focus {
    border-color: #000;
}

.weight-select:hover {
    border-color: #999;
}

/* Font Weights Section */
.font-weights-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.weights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.weight-item {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s;
    cursor: pointer;
}

.weight-item:hover {
    border-color: #000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.weight-preview {
    padding: 20px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 20px;
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
}

.weight-info {
    padding: 15px 20px;
}

.weight-name {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 16px;
}

.weight-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.weight-actions {
    display: flex;
    gap: 8px;
}

.weight-actions .action-btn {
    flex: 1;
    padding: 6px 12px;
    font-size: 12px;
}

/* Upload Modal Enhancements */
.upload-modal .modal-content {
    width: 500px;
    max-width: 90vw;
}

.upload-modal h2 {
    margin-bottom: 10px;
}

.upload-modal p {
    color: #666;
    margin-bottom: 20px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.2s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #999;
    background: #f8f9fa;
}

.upload-area.dragover {
    border-color: #000;
    background: #f0f0f0;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.upload-area p {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.upload-hint {
    font-size: 12px !important;
    color: #666 !important;
}

.upload-progress {
    text-align: center;
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: #000;
    transition: width 0.3s ease;
    width: 0%;
}

.upload-progress p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e5e5e5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .font-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .font-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .font-meta-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .weights-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .section-header h2 {
        font-size: 20px;
    }
    
    .upload-modal .modal-content {
        width: 95vw;
        margin: 20px;
        padding: 20px;
    }
    
    .upload-area {
        padding: 20px;
    }
    
    .upload-icon {
        font-size: 36px;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .preview-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .font-size-control {
        width: 100%;
        justify-content: space-between;
    }
    
    .weight-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .weight-actions .action-btn {
        flex: none;
    }
}

/* Animation for smooth transitions */
.weight-item,
.upload-area,
.action-btn,
.weight-select {
    transition: all 0.2s ease;
}

/* Focus states for accessibility */
.weight-select:focus,
.action-btn:focus {
    outline: 2px solid #000;
    outline-offset: 2px;
}

/* Loading state */
.weight-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.weight-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ddd;
    border-top-color: #000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
